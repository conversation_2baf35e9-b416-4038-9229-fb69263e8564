#!/usr/bin/env python3
"""
Setup ImageMagick for MoviePy on Windows
"""

import os
import sys
import platform
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_imagemagick():
    """Check if ImageMagick is installed"""
    try:
        result = subprocess.run(['magick', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("ImageMagick is already installed!")
            return True
    except FileNotFoundError:
        pass
    
    logger.info("ImageMagick not found")
    return False

def install_imagemagick_windows():
    """Provide instructions for installing ImageMagick on Windows"""
    logger.info("To install ImageMagick on Windows:")
    logger.info("1. Download ImageMagick from: https://imagemagick.org/script/download.php#windows")
    logger.info("2. Choose the version that matches your system (64-bit recommended)")
    logger.info("3. During installation, make sure to check 'Install development headers and libraries for C and C++'")
    logger.info("4. Add ImageMagick to your PATH environment variable")
    logger.info("5. Restart your command prompt/terminal")
    
    # Try to configure MoviePy to use ImageMagick
    try:
        import moviepy.config as config
        
        # Common ImageMagick installation paths on Windows
        possible_paths = [
            r"C:\Program Files\ImageMagick-7.1.1-Q16-HDRI\magick.exe",
            r"C:\Program Files\ImageMagick-7.1.0-Q16-HDRI\magick.exe",
            r"C:\Program Files (x86)\ImageMagick-7.1.1-Q16-HDRI\magick.exe",
            r"C:\Program Files (x86)\ImageMagick-7.1.0-Q16-HDRI\magick.exe",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found ImageMagick at: {path}")
                logger.info("You can configure MoviePy to use this path")
                return path
                
    except ImportError:
        pass
    
    return None

def configure_moviepy():
    """Configure MoviePy to work without ImageMagick for basic text"""
    logger.info("Configuring MoviePy to work without ImageMagick...")
    
    try:
        # Create a simple moviepy config
        config_content = '''
# MoviePy configuration
# This disables ImageMagick dependency for basic text rendering

import os
os.environ["IMAGEIO_FFMPEG_EXE"] = "ffmpeg"

# Use PIL for text rendering instead of ImageMagick
IMAGEMAGICK_BINARY = None
'''
        
        # Try to find moviepy config location
        import moviepy
        moviepy_dir = os.path.dirname(moviepy.__file__)
        config_path = os.path.join(moviepy_dir, "config_defaults.py")
        
        logger.info(f"MoviePy config would be at: {config_path}")
        logger.info("For now, the system will use PIL-based text rendering as fallback")
        
    except Exception as e:
        logger.error(f"Could not configure MoviePy: {e}")

def main():
    """Main setup function"""
    logger.info("Setting up ImageMagick for enhanced video features...")
    
    system = platform.system()
    
    if check_imagemagick():
        logger.info("ImageMagick is ready! Dynamic subtitles will work perfectly.")
        return
    
    if system == "Windows":
        install_imagemagick_windows()
    elif system == "Darwin":  # macOS
        logger.info("To install ImageMagick on macOS:")
        logger.info("Run: brew install imagemagick")
    elif system == "Linux":
        logger.info("To install ImageMagick on Linux:")
        logger.info("Ubuntu/Debian: sudo apt-get install imagemagick")
        logger.info("CentOS/RHEL: sudo yum install ImageMagick")
        logger.info("Arch: sudo pacman -S imagemagick")
    
    configure_moviepy()
    
    logger.info("\nNote: The system will work without ImageMagick using PIL-based text rendering.")
    logger.info("For the best dynamic subtitle experience, please install ImageMagick.")

if __name__ == "__main__":
    main()
