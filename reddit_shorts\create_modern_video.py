#!/usr/bin/env python3
"""
Modern Video Creator with Dynamic Word-by-Word Captions
Creates engaging short videos with animated text that appears word by word
"""

import os
import sys
import logging
import moviepy.editor as mpy
from pathlib import Path
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_word_clips(text, audio_duration, config):
    """Create individual word clips with timing for dynamic animation"""
    
    # Split text into words
    words = text.split()
    if not words:
        return []
    
    # Configuration
    max_words_per_group = config.get('video', {}).get('max_words_per_subtitle', 4)
    font_size = config.get('video', {}).get('text_overlay', {}).get('fontsize', 80)
    font_color = config.get('video', {}).get('text_overlay', {}).get('color', 'white')
    stroke_color = config.get('video', {}).get('text_overlay', {}).get('stroke_color', 'black')
    stroke_width = config.get('video', {}).get('text_overlay', {}).get('stroke_width', 3)
    
    # Group words (1-4 words per group for dynamic feel)
    word_groups = []
    i = 0
    while i < len(words):
        # Vary group size for dynamic effect
        if len(words) - i <= 2:
            group_size = len(words) - i
        elif len(words) - i <= max_words_per_group:
            group_size = min(3, len(words) - i)
        else:
            # Alternate between 2-4 words for variety
            group_size = 2 + (i % 3)  # 2, 3, 4, 2, 3, 4...
            group_size = min(group_size, max_words_per_group, len(words) - i)
        
        group = ' '.join(words[i:i + group_size])
        word_groups.append(group)
        i += group_size
    
    # Calculate timing for each group
    total_groups = len(word_groups)
    time_per_group = audio_duration / total_groups if total_groups > 0 else 1.0
    
    # Ensure minimum display time
    min_time = 0.8  # Minimum 0.8 seconds per group
    max_time = 3.0  # Maximum 3 seconds per group
    time_per_group = max(min_time, min(max_time, time_per_group))
    
    # Create text clips for each group
    text_clips = []
    start_time = 0
    
    for i, group in enumerate(word_groups):
        try:
            # Create text clip with modern styling
            txt_clip = mpy.TextClip(
                group,
                fontsize=font_size,
                color=font_color,
                stroke_color=stroke_color,
                stroke_width=stroke_width,
                font='Arial-Bold',
                method='caption'
            )
            
            # Position at bottom of screen (modern shorts style)
            txt_clip = txt_clip.set_position(('center', 0.8)).set_duration(time_per_group)
            
            # Add fade in/out for smooth transitions
            if i == 0:
                # First clip: fade in
                txt_clip = txt_clip.fadein(0.2)
            elif i == len(word_groups) - 1:
                # Last clip: fade out
                txt_clip = txt_clip.fadeout(0.2)
            else:
                # Middle clips: quick fade in/out
                txt_clip = txt_clip.fadein(0.1).fadeout(0.1)
            
            # Set start time
            txt_clip = txt_clip.set_start(start_time)
            
            text_clips.append(txt_clip)
            start_time += time_per_group
            
            logger.info(f"Created text clip {i+1}/{total_groups}: '{group}' at {start_time-time_per_group:.1f}s")
            
        except Exception as e:
            logger.error(f"Error creating text clip for '{group}': {e}")
            # Fallback: create simple text without effects
            try:
                txt_clip = mpy.TextClip(
                    group,
                    fontsize=font_size,
                    color=font_color,
                    font='Arial'
                ).set_position(('center', 0.8)).set_duration(time_per_group).set_start(start_time)
                
                text_clips.append(txt_clip)
                start_time += time_per_group
                
            except Exception as e2:
                logger.error(f"Failed to create fallback text clip: {e2}")
                continue
    
    return text_clips

def create_modern_video(background_path, audio_path, comment_text, output_path, config):
    """Create a modern video with dynamic word-by-word captions"""
    
    logger.info("Creating modern video with dynamic captions...")
    
    try:
        # 1. Load audio
        audio_clip = mpy.AudioFileClip(audio_path)
        duration = audio_clip.duration
        
        logger.info(f"Audio duration: {duration:.2f}s")
        
        # 2. Create or load background
        if background_path and os.path.exists(background_path):
            logger.info(f"Loading background video: {background_path}")
            background = mpy.VideoFileClip(background_path)
            
            # Loop background if it's shorter than audio
            if background.duration < duration:
                background = background.loop(duration=duration)
            else:
                background = background.subclip(0, duration)
                
            # Resize to target resolution
            target_size = tuple(config.get('video', {}).get('output_resolution', [1080, 1920]))
            background = background.resize(target_size)
            
            # Lower background audio volume
            bg_volume = config.get('video', {}).get('background_audio_volume', 0.1)
            if background.audio:
                background = background.volumex(bg_volume)
                
        else:
            # Create black background
            logger.info("Creating black background")
            target_size = tuple(config.get('video', {}).get('output_resolution', [1080, 1920]))
            background = mpy.ColorClip(size=target_size, color=(0, 0, 0), duration=duration)
        
        # 3. Add audio to background
        background = background.set_audio(audio_clip)
        
        # 4. Create dynamic text clips
        logger.info("Creating dynamic word-by-word text clips...")
        text_clips = create_word_clips(comment_text, duration, config)
        
        if not text_clips:
            logger.warning("No text clips created, using static text overlay")
            # Fallback to static text
            static_text = mpy.TextClip(
                comment_text,
                fontsize=60,
                color='white',
                font='Arial'
            ).set_position(('center', 0.8)).set_duration(duration)
            text_clips = [static_text]
        
        # 5. Composite all clips
        logger.info(f"Compositing video with {len(text_clips)} text clips...")
        all_clips = [background] + text_clips
        final_video = mpy.CompositeVideoClip(all_clips)
        
        # 6. Write video file
        logger.info(f"Writing video to: {output_path}")
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        final_video.write_videofile(
            output_path,
            fps=config.get('video', {}).get('output_fps', 30),
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='medium',
            logger=None
        )
        
        # 7. Clean up
        audio_clip.close()
        background.close()
        for clip in text_clips:
            clip.close()
        final_video.close()
        
        # Clean up temp files
        if os.path.exists('temp-audio.m4a'):
            try:
                os.remove('temp-audio.m4a')
            except:
                pass
        
        logger.info(f"Modern video created successfully: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"Error creating modern video: {e}")
        return None

def main():
    """Test the modern video creator"""
    if len(sys.argv) < 4:
        print("Usage: python create_modern_video.py <audio_path> <text> <output_path> [background_path]")
        return
    
    audio_path = sys.argv[1]
    text = sys.argv[2]
    output_path = sys.argv[3]
    background_path = sys.argv[4] if len(sys.argv) > 4 else None
    
    # Default config
    config = {
        'video': {
            'output_resolution': [1080, 1920],
            'output_fps': 30,
            'background_audio_volume': 0.1,
            'max_words_per_subtitle': 4,
            'text_overlay': {
                'fontsize': 80,
                'color': 'white',
                'stroke_color': 'black',
                'stroke_width': 3
            }
        }
    }
    
    result = create_modern_video(background_path, audio_path, text, output_path, config)
    
    if result:
        print(f"✅ Modern video created: {result}")
    else:
        print("❌ Failed to create video")

if __name__ == "__main__":
    main()
