#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import reddit

def test_reddit_fetch():
    print("Testing Reddit fetch...")
    
    # Get a top post from AskReddit
    url = reddit.get_top_post_url_from_subreddit("AskReddit")
    print(f"Got URL: {url}")
    
    if url:
        # Fetch the submission
        submission = reddit.fetch_submission(url)
        print(f"Got submission: {submission}")
        
        if submission:
            # Get comments
            comments = reddit.get_comments(submission, limit=10)
            print(f"Got {len(comments)} comments")
            
            for i, comment in enumerate(comments[:5]):
                print(f"Comment {i+1}: {comment}")
                print("---")

if __name__ == "__main__":
    test_reddit_fetch()
