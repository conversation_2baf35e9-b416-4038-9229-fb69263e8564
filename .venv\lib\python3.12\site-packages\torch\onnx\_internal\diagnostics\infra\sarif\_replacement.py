# DO NOT EDIT! This file was generated by jschema_to_python version 0.0.1.dev29,
# with extension for dataclasses and type annotation.

from __future__ import annotations

import dataclasses
from typing import Optional

from torch.onnx._internal.diagnostics.infra.sarif import (
    _artifact_content,
    _property_bag,
    _region,
)


@dataclasses.dataclass
class Replacement(object):
    """The replacement of a single region of an artifact."""

    deleted_region: _region.Region = dataclasses.field(
        metadata={"schema_property_name": "deletedRegion"}
    )
    inserted_content: Optional[_artifact_content.ArtifactContent] = dataclasses.field(
        default=None, metadata={"schema_property_name": "insertedContent"}
    )
    properties: Optional[_property_bag.PropertyBag] = dataclasses.field(
        default=None, metadata={"schema_property_name": "properties"}
    )


# flake8: noqa
