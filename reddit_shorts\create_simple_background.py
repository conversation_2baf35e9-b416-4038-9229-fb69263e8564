#!/usr/bin/env python3
"""
Create a simple but effective Minecraft-style background quickly
"""

import logging
import moviepy.editor as mpy
import numpy as np
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_simple_minecraft_bg():
    """Create a simple but effective Minecraft-style background"""
    
    backgrounds_dir = Path("backgrounds")
    backgrounds_dir.mkdir(exist_ok=True)
    
    def make_frame(t):
        """Create a frame with moving Minecraft-style blocks"""
        width, height = 1080, 1920
        
        # Create base frame
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Minecraft-inspired colors (more realistic)
        colors = [
            [34, 139, 34],    # <PERSON> Green
            [139, 69, 19],    # <PERSON> Wood
            [128, 128, 128],  # <PERSON>
            [160, 82, 45],    # Dirt
            [70, 70, 70],     # Cobblestone
            [101, 67, 33],    # Dark Oak
        ]
        
        # Block size and movement
        block_size = 120
        speed = 20
        
        # Calculate offset for movement
        offset_x = int(t * speed) % block_size
        offset_y = int(t * speed * 0.7) % block_size
        
        # Draw blocks in a grid pattern
        for y in range(-block_size, height + block_size, block_size):
            for x in range(-block_size, width + block_size, block_size):
                # Apply movement offset
                block_x = x - offset_x
                block_y = y - offset_y
                
                # Choose color based on position (creates consistent pattern)
                color_index = ((block_x // block_size) + (block_y // block_size)) % len(colors)
                color = colors[color_index]
                
                # Draw the block
                start_x = max(0, block_x)
                end_x = min(width, block_x + block_size)
                start_y = max(0, block_y)
                end_y = min(height, block_y + block_size)
                
                if start_x < end_x and start_y < end_y:
                    # Fill the block
                    frame[start_y:end_y, start_x:end_x] = color
                    
                    # Add simple 3D effect (lighter top/left, darker bottom/right)
                    border_size = 8
                    
                    # Top border (lighter)
                    if start_y < end_y - border_size:
                        lighter_color = [min(255, c + 30) for c in color]
                        frame[start_y:start_y + border_size, start_x:end_x] = lighter_color
                    
                    # Left border (lighter)
                    if start_x < end_x - border_size:
                        lighter_color = [min(255, c + 20) for c in color]
                        frame[start_y:end_y, start_x:start_x + border_size] = lighter_color
                    
                    # Bottom border (darker)
                    if end_y > start_y + border_size:
                        darker_color = [max(0, c - 30) for c in color]
                        frame[end_y - border_size:end_y, start_x:end_x] = darker_color
                    
                    # Right border (darker)
                    if end_x > start_x + border_size:
                        darker_color = [max(0, c - 20) for c in color]
                        frame[start_y:end_y, end_x - border_size:end_x] = darker_color
        
        return frame
    
    try:
        # Create a short video clip
        logger.info("Creating Minecraft-style background...")
        clip = mpy.VideoClip(make_frame, duration=10)
        clip = clip.set_fps(10)  # Low FPS for speed
        
        output_path = backgrounds_dir / "minecraft_style_background.mp4"
        
        # Write with fastest settings
        clip.write_videofile(
            str(output_path),
            codec='libx264',
            preset='ultrafast',
            verbose=False,
            logger=None,
            ffmpeg_params=['-crf', '30']
        )
        
        clip.close()
        logger.info(f"Background created: {output_path}")
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Failed to create background: {e}")
        return None

if __name__ == "__main__":
    create_simple_minecraft_bg()
