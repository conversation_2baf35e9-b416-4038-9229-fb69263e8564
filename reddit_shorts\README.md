# Reddit Shorts Automation

A fully automated system to create modern, engaging short-form videos from Reddit posts with dynamic text-to-speech narration and animated captions.

## ✨ Features

- **🤖 Fully Automated**: One command creates complete videos
- **📱 Modern Design**: Dynamic word-by-word subtitles (max 5 words at a time)
- **🎮 Minecraft Background**: Animated Minecraft-style background video
- **🧠 Smart AI Selection**: Intelligently selects the best comments based on length, engagement, and sentiment
- **🎵 Multiple TTS Engines**: Support for gTTS, pyttsx3, Google Cloud TTS, ElevenLabs, and XTTS
- **⚡ Fast Processing**: Caching system avoids regenerating content
- **📺 Shorts Optimized**: Perfect vertical format (1080x1920) for TikTok, YouTube Shorts, Instagram Reels
- **🎨 Professional Styling**: Bold fonts with stroke outlines for maximum readability

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Up Background Video

```bash
python download_background.py
```

This creates a Minecraft-style animated background automatically.

### 3. Run the Demo

```bash
python demo.py
```

Interactive demo that shows all features and lets you choose a subreddit.

### 4. Create Videos

**🎯 Simple automated mode** (tries multiple subreddits automatically):
```bash
python create_automated_short.py
```

**📝 Specific subreddit**:
```bash
python create_automated_short.py --subreddit AmItheAsshole
```

**🎬 Direct creation**:
```bash
python reddit_to_shorts.py maliciouscompliance
```

### 3. Manual Control

For more control, use the main script directly:

```bash
# Use a specific subreddit
python reddit_to_shorts.py AmItheAsshole

# Use a specific Reddit URL
python reddit_to_shorts.py "https://www.reddit.com/r/AmItheAsshole/comments/..."

# With background video
python reddit_to_shorts.py AmItheAsshole background_video.mp4

# Custom output path
python reddit_to_shorts.py AmItheAsshole -o my_video.mp4

# Different TTS engine
python reddit_to_shorts.py AmItheAsshole --tts elevenlabs
```

## Configuration

Edit `config.yaml` to customize:

- **TTS Engine**: Choose between gTTS, pyttsx3, Google Cloud TTS, ElevenLabs, or XTTS
- **Comment Filtering**: Adjust minimum/maximum word counts and duration
- **Video Settings**: Resolution, FPS, text styling
- **Caching**: Enable/disable audio caching

## Recommended Subreddits

The automation works best with subreddits that have longer, story-based comments:

- `AmItheAsshole` - Moral dilemma discussions
- `tifu` - "Today I F***ed Up" stories
- `maliciouscompliance` - Workplace revenge stories
- `pettyrevenge` - Small revenge stories
- `relationship_advice` - Relationship discussions
- `entitledparents` - Entitled people stories
- `choosingbeggars` - Unreasonable request stories

## TTS Engine Setup

### gTTS (Default - Free)
No setup required. Uses Google's free TTS service.

### ElevenLabs (High Quality)
1. Get API key from [ElevenLabs](https://elevenlabs.io)
2. Set in config.yaml or environment variable `ELEVEN_API_KEY`

### Google Cloud TTS (Professional)
1. Set up Google Cloud account and enable TTS API
2. Download service account key
3. Set `GOOGLE_APPLICATION_CREDENTIALS` environment variable

### XTTS (Voice Cloning)
1. Install PyTorch: `pip install torch torchvision torchaudio`
2. Install TTS: `pip install TTS`
3. Provide speaker reference WAV file in config

## Output

Videos are saved to the `output/` directory with names like:
- `output_short_abc123.mp4` (manual mode)
- `automated_short_subreddit_timestamp.mp4` (automated mode)

## Troubleshooting

### No suitable comments found
- Try a different subreddit
- Lower the `min_words` setting in config.yaml
- Check if the subreddit has recent active posts

### TTS errors
- Ensure internet connection for gTTS
- Check API keys for premium services
- Try switching to pyttsx3 for offline TTS

### Video creation errors
- Ensure FFmpeg is installed
- Check available disk space
- Try with a simpler background or no background

## File Structure

```
reddit_shorts/
├── reddit_to_shorts.py      # Main script
├── create_automated_short.py # Automation wrapper
├── config.yaml              # Configuration file
├── requirements.txt         # Dependencies
├── utils/                   # Core modules
│   ├── reddit.py           # Reddit API handling
│   ├── tts.py              # Text-to-speech engines
│   ├── video.py            # Video creation
│   └── text.py             # Text processing
├── cache/                   # Cached audio files
│   └── audio/
└── output/                  # Generated videos
```

## License

This project is for educational purposes. Respect Reddit's terms of service and content creators' rights.
