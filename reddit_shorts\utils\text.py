# utils/text.py
import re
import logging
import sys
import os
import uuid
import nltk
from nltk.sentiment import SentimentIntensityAnalyzer
from nltk.tokenize import word_tokenize
import moviepy.editor as mpy

# Configure logging properly
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Download necessary NLTK data
print("Checking NLTK data...")
try:
    # Force download punkt and vader_lexicon
    print("Downloading NLTK punkt tokenizer...")
    nltk.download('punkt', quiet=True)
    print("Downloading NLTK vader lexicon...")
    nltk.download('vader_lexicon', quiet=True)
    print("NLTK data downloaded successfully")
    logger.info("NLTK data prepared successfully.")
except Exception as download_exc:
    print(f"ERROR downloading NLTK data: {download_exc}")
    logger.error(f"Failed to download NLTK data: {download_exc}")
    logger.warning("Sentiment analysis and word tokenization might not work properly.")

# Initialize sentiment analyzer once
try:
    sia = SentimentIntensityAnalyzer()
except LookupError:
    logger.error("Could not initialize SentimentIntensityAnalyzer. Make sure 'vader_lexicon' is downloaded.")
    sia = None

# --- Text Cleaning ---
def remove_markdown(text):
    """Removes common markdown formatting."""
    # Remove bold/italics
    text = re.sub(r'\*\*?(.*?)\*\*?', r'\1', text)
    # Remove links but keep link text
    text = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', text)
    # Remove inline code
    text = re.sub(r'`(.*?)`', r'\1', text)
    # Remove blockquotes
    text = re.sub(r'^> ?(.*?)$', r'\1', text, flags=re.MULTILINE)
    # Remove horizontal rules
    text = re.sub(r'^[-_*]{3,}$', '', text, flags=re.MULTILINE)
    # Remove headers
    text = re.sub(r'^#+ ?(.*?)$', r'\1', text, flags=re.MULTILINE)
    return text.strip()

def remove_urls(text):
    """Removes URLs from text."""
    return re.sub(r'https?://\S+|www\.\S+', '', text)

def clean_comment(text):
    """Applies cleaning steps to comment text."""
    original_length = len(text)
    text = remove_markdown(text)
    text = remove_urls(text)
    # Replace common Reddit edits
    text = re.sub(r'edit:.*$', '', text, flags=re.IGNORECASE | re.MULTILINE)
    # Normalize whitespace
    text = re.sub(r'\s+', ' ', text).strip()
    
    new_length = len(text)
    if original_length != new_length:
        logger.debug(f"Cleaned comment text: {original_length} -> {new_length} chars ({(new_length/original_length)*100:.1f}%)")
    
    return text

# --- Duration Estimation ---
def count_words(text):
    """Counts words in a text using NLTK."""
    try:
        tokens = word_tokenize(text)
        # Filter out punctuation
        words = [word for word in tokens if word.isalnum()]
        return len(words)
    except LookupError:
        logger.warning("NLTK 'punkt' tokenizer not found. Falling back to simple split for word count.")
        return len(text.split())
    except Exception as e:
        logger.error(f"Error counting words: {e}")
        return len(text.split()) # Fallback

def estimate_duration(text, words_per_minute=150):
    """Estimates speech duration in seconds based on word count."""
    word_count = count_words(text)
    if word_count == 0:
        return 0
    # Slightly slower default speaking rate for better estimate
    duration_minutes = word_count / words_per_minute
    duration_seconds = duration_minutes * 60
    logger.debug(f"Estimated duration: {word_count} words → {duration_seconds:.1f} seconds (at {words_per_minute} WPM)")
    return duration_seconds

import uuid

def split_sentences(text):
    try:
        return nltk.sent_tokenize(text)
    except:
        return text.split('. ')  # fallback

def format_timestamp(s):
    h = int(s//3600)
    m = int((s%3600)//60)
    sec = int(s%60)
    ms = int((s - int(s))*1000)
    return f"{h:02d}:{m:02d}:{sec:02d},{ms:03d}"

def generate_dynamic_srt(text, audio_path, config):
    """Generate dynamic SRT subtitle file with word-by-word timing (max 5 words per subtitle)"""
    try:
        # Get actual audio duration
        audio_clip = mpy.AudioFileClip(audio_path)
        audio_dur = audio_clip.duration
        audio_clip.close()

        # Split text into words
        words = text.split()
        if not words:
            return None

        # Group words into chunks of 1-5 words
        word_chunks = []
        max_words_per_chunk = config.get('video', {}).get('max_words_per_subtitle', 5)

        i = 0
        while i < len(words):
            # Determine chunk size (1-5 words, prefer shorter for better readability)
            remaining_words = len(words) - i

            if remaining_words <= 2:
                chunk_size = remaining_words
            elif remaining_words <= 5:
                chunk_size = min(3, remaining_words)
            else:
                # Vary chunk size for dynamic feel
                chunk_size = min(max_words_per_chunk, 2 + (i % 3))  # 2-4 words mostly

            chunk = ' '.join(words[i:i + chunk_size])
            word_chunks.append(chunk)
            i += chunk_size

        # Calculate timing for each chunk
        total_words = len(words)
        words_per_second = total_words / audio_dur if audio_dur > 0 else 2

        # Generate SRT content
        srt_lines = []
        start = 0.0

        for i, chunk in enumerate(word_chunks, start=1):
            # Calculate duration based on word count in chunk
            chunk_word_count = len(chunk.split())
            duration = chunk_word_count / words_per_second

            # Minimum duration of 0.5 seconds, maximum of 3 seconds
            duration = max(0.5, min(3.0, duration))

            end = start + duration

            # Ensure we don't exceed total audio duration
            if end > audio_dur:
                end = audio_dur
                if start >= audio_dur:
                    break

            srt_lines.append(f"{i}")
            srt_lines.append(f"{format_timestamp(start)} --> {format_timestamp(end)}")
            srt_lines.append(chunk.strip())
            srt_lines.append("")  # blank line

            start = end

        # Save SRT file
        cache_dir = config.get('cache', {}).get('directory', 'cache')
        os.makedirs(cache_dir, exist_ok=True)
        srt_file = os.path.join(cache_dir, f"dynamic_subtitles_{uuid.uuid4().hex[:8]}.srt")

        with open(srt_file, 'w', encoding='utf-8') as f:
            f.write("\n".join(srt_lines))

        logger.info(f"Generated dynamic SRT file: {srt_file} with {len(word_chunks)} subtitle segments")
        return srt_file

    except Exception as e:
        logger.error(f"Failed to generate dynamic SRT file: {e}")
        return None

def generate_srt(text, audio_path, config):
    """Generate SRT subtitle file - uses dynamic mode if enabled"""
    dynamic_mode = config.get('video', {}).get('dynamic_subtitles', True)

    if dynamic_mode:
        return generate_dynamic_srt(text, audio_path, config)
    else:
        # Original sentence-based approach
        try:
            # Get actual audio duration
            audio_clip = mpy.AudioFileClip(audio_path)
            audio_dur = audio_clip.duration
            audio_clip.close()

            # Split text into sentences
            sentences = split_sentences(text)
            if not sentences:
                sentences = [text]  # fallback to whole text

            # Estimate duration for each sentence
            durations = [estimate_duration(sent, words_per_minute=150) for sent in sentences]

            # Normalize durations so they sum exactly to audio duration
            total = sum(durations)
            if total > 0:
                scale = audio_dur / total
                durations = [d * scale for d in durations]
            else:
                # Fallback: equal time for each sentence
                durations = [audio_dur / len(sentences)] * len(sentences)

            # Generate SRT content
            srt_lines = []
            start = 0.0
            for i, (sent, dur) in enumerate(zip(sentences, durations), start=1):
                end = start + dur
                srt_lines.append(f"{i}")
                srt_lines.append(f"{format_timestamp(start)} --> {format_timestamp(end)}")
                srt_lines.append(sent.strip())
                srt_lines.append("")  # blank line
                start = end

            # Save SRT file
            cache_dir = config.get('cache', {}).get('directory', 'cache')
            os.makedirs(cache_dir, exist_ok=True)
            srt_file = os.path.join(cache_dir, f"subtitles_{uuid.uuid4().hex[:8]}.srt")

            with open(srt_file, 'w', encoding='utf-8') as f:
                f.write("\n".join(srt_lines))

            logger.info(f"Generated SRT file: {srt_file} with {len(sentences)} subtitle segments")
            return srt_file

        except Exception as e:
            logger.error(f"Failed to generate SRT file: {e}")
            return None

# --- Comment Filtering and Ranking ---
def filter_comments(comments, config):
    """Filters comments based on length and keywords."""
    min_words = config.get('min_words', 50)
    max_words = config.get('max_words', 300)
    filter_keywords = [kw.lower() for kw in config.get('filter_keywords', [])]

    excluded_count = 0
    excluded_too_short = 0
    excluded_too_long = 0
    excluded_keywords = 0
    excluded_patterns = 0

    # Debug: Show first few comments
    logger.info(f"Debugging first 5 comments:")
    for i, comment in enumerate(comments[:5]):
        text = comment.get('body', comment.get('text', ''))
        word_count = count_words(text)
        logger.info(f"  Comment {i+1}: {word_count} words - '{text[:100]}...'")

    filtered = []
    for comment in comments:
        text = comment.get('body', comment.get('text', ''))
        word_count = count_words(text)

        if word_count < min_words:
            excluded_too_short += 1
            continue
            
        if word_count > max_words:
            excluded_too_long += 1
            continue

        text_lower = text.lower()
        if any(keyword in text_lower for keyword in filter_keywords):
            excluded_keywords += 1
            continue

        # Add basic check for non-narrative/spammy patterns
        if (text.startswith(("This!", "Came here to say this", "Underrated comment")) or 
            "[deleted]" in text or "[removed]" in text):
            excluded_patterns += 1
            continue

        filtered.append(comment)

    excluded_count = excluded_too_short + excluded_too_long + excluded_keywords + excluded_patterns
    logger.info(f"Filtered {len(comments)} comments: {len(filtered)} passed, {excluded_count} excluded")
    
    # Detail reasons for exclusions if any comments were excluded
    if excluded_count > 0:
        logger.info(f"  • {excluded_too_short} too short (<{min_words} words)")
        logger.info(f"  • {excluded_too_long} too long (>{max_words} words)")
        logger.info(f"  • {excluded_keywords} containing filtered keywords")
        logger.info(f"  • {excluded_patterns} with problematic patterns")
    
    return filtered

def get_sentiment_score(text):
    """Returns the compound sentiment score using VADER."""
    if not sia:
        return 0.0 # Return neutral if analyzer failed to load
    try:
        return sia.polarity_scores(text)['compound']
    except Exception as e:
        logger.error(f"Error getting sentiment score: {e}")
        return 0.0

def rank_comments(comments, config):
    """Ranks comments based on duration, engagement, and sentiment."""
    target_duration = config.get('target_duration_seconds', 65)
    min_duration = config.get('min_duration_seconds', 60)
    ranked = []
    
    # Count comments that meet minimum duration
    valid_duration_count = 0
    
    logger.info(f"Ranking {len(comments)} comments (target: {target_duration}s, minimum: {min_duration}s)")

    for comment in comments:
        cleaned_text = clean_comment(comment['body'])
        duration = estimate_duration(cleaned_text)
        sentiment = get_sentiment_score(cleaned_text)
        score = comment['score'] # Reddit score
        
        # Track if this comment meets minimum duration
        meets_min_duration = duration >= min_duration
        if meets_min_duration:
            valid_duration_count += 1

        # Skip comments that are too long even after cleaning
        # Allow a buffer of 10% over target duration
        if duration > target_duration * 1.1:
            logger.debug(f"Comment {comment['id']} skipped - too long ({duration:.1f}s > {target_duration * 1.1:.1f}s)")
            continue

        # Scoring - prioritize duration first, then other factors
        # Heavily penalize comments that don't meet minimum duration
        if not meets_min_duration:
            # Add severe penalty for comments under minimum duration
            duration_penalty = 500 + (min_duration - duration) * 50
        else:
            # For comments that meet minimum, penalize distance from target
            duration_penalty = abs(duration - target_duration) * 2
            
        # Calculate final score - higher is better
        ranking_score = score + abs(sentiment) * 10 - duration_penalty

        ranked.append({
            'id': comment['id'],
            'text': cleaned_text,
            'duration': duration,
            'score': score,
            'sentiment': sentiment,
            'meets_min_duration': meets_min_duration,
            'ranking_score': ranking_score,
            'original_comment': comment # Keep original for reference
        })

    # Sort by ranking score, descending
    ranked.sort(key=lambda x: x['ranking_score'], reverse=True)
    
    if not ranked:
        logger.warning("No comments matched the ranking criteria")
        return []
        
    # Log duration statistics
    logger.info(f"Duration stats: {valid_duration_count} of {len(comments)} comments meet minimum {min_duration}s duration")
    
    # Show top 3 comments with their scores
    logger.info(f"Top ranked comments:")
    for i, comment in enumerate(ranked[:3]):
        if i >= len(ranked):
            break
        duration_indicator = "OK" if comment['meets_min_duration'] else "SHORT"
        logger.info(f"  #{i+1}: Score {comment['ranking_score']:.1f}, {comment['duration']:.1f}s {duration_indicator}, {len(comment['text'])} chars, Reddit score: {comment['score']}")
    
    # If we have comments meeting minimum duration, filter out those that don't
    # unless doing so would leave us with no comments
    min_duration_comments = [c for c in ranked if c['meets_min_duration']]
    if min_duration_comments:
        logger.info(f"Filtering to {len(min_duration_comments)} comments meeting minimum duration")
        return min_duration_comments
    else:
        logger.warning(f"No comments meet the minimum duration of {min_duration}s. Using best available.")
        
    return ranked

def select_best_comment(comments, config):
    """Selects the best comment based on filtering and ranking."""
    comment_config = config.get('comment', {})
    
    if not comments or len(comments) == 0:
        logger.warning("No comments to select from")
        return None
        
    logger.info(f"Selecting best comment from {len(comments)} comments")
    filtered = filter_comments(comments, comment_config)
    
    if not filtered:
        logger.warning("No comments passed the filtering criteria")
        return None

    ranked = rank_comments(filtered, comment_config)
    if not ranked:
        logger.warning("Failed to rank comments")
        return None

    best_comment = ranked[0]
    logger.info(f"Selected comment: {best_comment['id']}")
    logger.info(f"  • Duration: {best_comment['duration']:.1f}s")
    logger.info(f"  • Reddit score: {best_comment['score']}")
    logger.info(f"  • Sentiment: {best_comment['sentiment']:.2f}")
    logger.info(f"  • Length: {len(best_comment['text'])} chars, {count_words(best_comment['text'])} words")
    
    return best_comment