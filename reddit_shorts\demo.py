#!/usr/bin/env python3
"""
Demo script showing all features of the Reddit Shorts Automation system
"""

import os
import sys
import time
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def print_header():
    """Print demo header"""
    print("=" * 60)
    print("  REDDIT SHORTS AUTOMATION - DEMO")
    print("  Fully Automated Short Video Creator")
    print("=" * 60)
    print()

def demo_features():
    """Demonstrate all features"""
    print("🎬 FEATURES DEMONSTRATED:")
    print("✅ Automatic Reddit content fetching")
    print("✅ Smart comment selection (length, engagement, sentiment)")
    print("✅ Text-to-speech with gTTS")
    print("✅ Dynamic word-by-word subtitles (max 4 words at a time)")
    print("✅ Minecraft-style animated background")
    print("✅ Modern text styling with stroke/outline")
    print("✅ Vertical format optimized for shorts (1080x1920)")
    print("✅ Audio caching for faster re-runs")
    print()

def run_demo():
    """Run the demo"""
    print_header()
    demo_features()
    
    # Check if background exists
    bg_path = Path("backgrounds/minecraft_style_background.mp4")
    if not bg_path.exists():
        print("🎨 Creating Minecraft-style background...")
        os.system("python download_background.py")
        print()
    
    # Demo subreddits with good content
    demo_subreddits = [
        ("maliciouscompliance", "Workplace revenge stories"),
        ("AmItheAsshole", "Moral dilemma discussions"),
        ("tifu", "Today I F***ed Up stories"),
        ("pettyrevenge", "Small revenge stories"),
    ]
    
    print("🎯 DEMO SUBREDDITS:")
    for i, (subreddit, description) in enumerate(demo_subreddits, 1):
        print(f"  {i}. r/{subreddit} - {description}")
    print()
    
    # Let user choose or auto-select
    try:
        choice = input("Choose a subreddit (1-4) or press Enter for automatic selection: ").strip()
        
        if choice and choice.isdigit() and 1 <= int(choice) <= len(demo_subreddits):
            selected_subreddit = demo_subreddits[int(choice) - 1][0]
        else:
            selected_subreddit = demo_subreddits[0][0]  # Default to maliciouscompliance
            
    except KeyboardInterrupt:
        print("\nDemo cancelled.")
        return
    
    print(f"🚀 Creating video from r/{selected_subreddit}...")
    print()
    
    # Run the automation
    start_time = time.time()
    
    cmd = f"python reddit_to_shorts.py {selected_subreddit}"
    result = os.system(cmd)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print()
    print("=" * 60)
    
    if result == 0:
        print("🎉 SUCCESS! Video created successfully!")
        print(f"⏱️  Total time: {duration:.1f} seconds")
        
        # Show output files
        output_dir = Path("output")
        if output_dir.exists():
            videos = list(output_dir.glob("*.mp4"))
            if videos:
                latest_video = max(videos, key=os.path.getctime)
                file_size = latest_video.stat().st_size / 1024  # KB
                
                print(f"📁 Output: {latest_video}")
                print(f"📊 Size: {file_size:.1f} KB")
                print()
                print("🎬 FEATURES USED:")
                print("  ✅ Dynamic subtitles (word-by-word)")
                print("  ✅ Minecraft-style background")
                print("  ✅ High-quality TTS audio")
                print("  ✅ Modern text styling")
                print("  ✅ Vertical format (1080x1920)")
                
    else:
        print("❌ Demo failed. Check the logs above for details.")
    
    print("=" * 60)

def show_config_info():
    """Show configuration information"""
    print("\n📋 CURRENT CONFIGURATION:")
    
    config_path = Path("config.yaml")
    if config_path.exists():
        print("  ✅ Config file found")
        print("  🎨 Dynamic subtitles: ENABLED")
        print("  📝 Max words per subtitle: 4")
        print("  🎵 TTS Engine: gTTS (free)")
        print("  🎬 Background: Minecraft-style")
        print("  📱 Format: Vertical (1080x1920)")
    else:
        print("  ❌ Config file not found")
    
    # Check dependencies
    print("\n🔧 DEPENDENCIES:")
    
    try:
        import moviepy
        print("  ✅ MoviePy: Installed")
    except ImportError:
        print("  ❌ MoviePy: Not installed")
    
    try:
        import gtts
        print("  ✅ gTTS: Installed")
    except ImportError:
        print("  ❌ gTTS: Not installed")
    
    try:
        import nltk
        print("  ✅ NLTK: Installed")
    except ImportError:
        print("  ❌ NLTK: Not installed")
    
    # Check ImageMagick
    try:
        import subprocess
        result = subprocess.run(['magick', '-version'], capture_output=True)
        if result.returncode == 0:
            print("  ✅ ImageMagick: Installed (dynamic subtitles available)")
        else:
            print("  ⚠️  ImageMagick: Not found (using PIL fallback)")
    except:
        print("  ⚠️  ImageMagick: Not found (using PIL fallback)")

def main():
    """Main demo function"""
    if len(sys.argv) > 1 and sys.argv[1] == "--info":
        show_config_info()
        return
    
    try:
        run_demo()
        show_config_info()
        
        print("\n🎯 NEXT STEPS:")
        print("  1. Check the output folder for your video")
        print("  2. Try different subreddits: python reddit_to_shorts.py <subreddit>")
        print("  3. Use automation: python create_automated_short.py")
        print("  4. Install ImageMagick for better subtitles: python setup_imagemagick.py")
        print()
        print("📚 For more options, see README.md")
        
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user.")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        logger.error(f"Demo failed: {e}")

if __name__ == "__main__":
    main()
