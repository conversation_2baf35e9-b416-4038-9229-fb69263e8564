# reddit_to_shorts.py
import argparse
import yaml
import os
import logging
import sys
import traceback
from pathlib import Path
from utils import reddit, tts, video, text

# DIRECT CONSOLE OUTPUT FOR DEBUGGING
print("\n\n==== STARTING REDDIT TO SHORTS ====\n")

# Configure logging to show all messages in the console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# Constants
CONFIG_FILE = "config.yaml"
VERSION = "1.0.0"

def load_config():
    """Loads the configuration from config.yaml."""
    print(f"Loading config from: {os.path.abspath(CONFIG_FILE)}")
    
    if not os.path.exists(CONFIG_FILE):
        print(f"ERROR: Config file not found at {os.path.abspath(CONFIG_FILE)}")
        logger.error(f"Configuration file not found: {CONFIG_FILE}")
        logger.error(f"Working directory: {os.getcwd()}")
        return None
        
    try:
        with open(CONFIG_FILE, 'r') as f:
            config = yaml.safe_load(f)
        print(f"Config loaded successfully")
        logger.info(f"Configuration loaded successfully from {os.path.abspath(CONFIG_FILE)}")
        return config
    except Exception as e:
        print(f"ERROR loading config: {e}")
        logger.error(f"Error loading configuration: {e}")
        return None

def parse_arguments():
    """Parses command-line arguments."""
    parser = argparse.ArgumentParser(
        description=f"Reddit to Shorts v{VERSION} - Create videos from Reddit threads."
    )
    
    # Required and positional arguments
    parser.add_argument(
        "reddit_source", 
        help="URL of the Reddit thread OR name of the subreddit (e.g., 'AmItheAsshole')."
    )
    
    # Optional arguments
    parser.add_argument(
        "background_video", 
        nargs='?', 
        default=None, 
        help="Path to the background video file. If omitted, a black background will be used."
    )
    parser.add_argument(
        "-o", "--output", 
        help="Path for the output video file. Default uses template from config."
    )
    parser.add_argument(
        "--tts", 
        choices=["gTTS", "pyttsx3", "XTTS"],
        help="Override TTS engine. Defaults to config setting."
    )
    parser.add_argument(
        "--speaker_wav", 
        help="Path to a reference speaker WAV file for XTTS voice cloning."
    )
    parser.add_argument(
        "--no-cache", 
        action="store_true", 
        help="Disable caching for this run."
    )
    parser.add_argument(
        "-v", "--verbose", 
        action="store_true", 
        help="Show detailed debug information."
    )
    
    return parser.parse_args()

def setup_logging(verbose=False):
    """Configure logging level based on verbosity."""
    root_logger = logging.getLogger()
    
    if verbose:
        root_logger.setLevel(logging.DEBUG)
        logger.debug("Debug logging enabled")
    else:
        root_logger.setLevel(logging.INFO)

def print_banner():
    """Print a banner with app info at startup."""
    banner = f"""
===============================================
  REDDIT TO SHORTS v{VERSION}
  Convert Reddit threads to short videos
===============================================
    """
    print(banner)

def main():
    """Main application logic."""
    try:
        # Print banner and parse arguments
        print_banner()
        print("Parsing command line arguments...")
        args = parse_arguments()
        setup_logging(args.verbose)
        
        # Load configuration
        print("Starting conversion process...")
        logger.info("Starting Reddit to Shorts conversion...")
        config = load_config()
        if not config:
            print("ERROR: Failed to load configuration")
            logger.error("Failed to load configuration. Exiting.")
            sys.exit(1)
            
        # Determine Reddit URL (handle URL vs Subreddit name)
        reddit_source_input = args.reddit_source
        reddit_url = None

        if reddit_source_input.lower().startswith('http'):
            reddit_url = reddit_source_input
            logger.info(f"Reddit source is a URL: {reddit_url}")
        else:
            # Assume it's a subreddit name
            subreddit_name = reddit_source_input
            logger.info(f"Reddit source is a subreddit name: r/{subreddit_name}")
            reddit_url = reddit.get_top_post_url_from_subreddit(subreddit_name)
            if not reddit_url:
                print(f"ERROR: Could not find a top post URL for subreddit r/{subreddit_name}")
                logger.error(f"Failed to retrieve top post URL for r/{subreddit_name}. Exiting.")
                sys.exit(1)
            logger.info(f"Using top post URL: {reddit_url}")

        print(f"Using Reddit URL: {reddit_url}")
        
        # Override config with CLI arguments
        if args.tts:
            config['tts_engine'] = args.tts
            print(f"Using TTS engine (from args): {args.tts}")
            logger.info(f"Using TTS engine: {args.tts} (from command line)")
        else:
            print(f"Using TTS engine (from config): {config.get('tts_engine', 'gTTS')}")
            logger.info(f"Using TTS engine: {config.get('tts_engine', 'gTTS')} (from config)")
            
        if args.speaker_wav:
            if 'xtts' not in config:
                config['xtts'] = {}
            config['xtts']['speaker_wav'] = args.speaker_wav
            logger.info(f"Using custom speaker reference: {args.speaker_wav}")
            
        if args.no_cache:
            if 'cache' not in config:
                config['cache'] = {}
            config['cache']['enabled'] = False
            logger.info("Cache disabled for this run")
            
        # Validate background video if provided
        if args.background_video:
            if not os.path.exists(args.background_video):
                logger.error(f"Background video not found: {args.background_video}")
                logger.error("Please provide a valid video file path or omit for black background")
                sys.exit(1)
            logger.info(f"Using background video: {args.background_video}")
        else:
            logger.info("No background video provided. Using black background.")

        # ===== STEP 1: Fetch Reddit Content =====
        print(f"\nFetching content from Reddit URL: {reddit_url}")
        logger.info(f"Fetching Reddit content from: {reddit_url}")
        submission = reddit.fetch_submission(reddit_url)
        
        if not submission:
            print("ERROR: Failed to fetch Reddit post")
            logger.error("Failed to fetch Reddit submission. Please check the URL and try again.")
            sys.exit(1)
            
        # Get title and post info
        title = submission.get('title', 'Untitled') if isinstance(submission, dict) else submission.title
        logger.info(f"Reddit post: \"{title}\"")
        
        # Get comments
        sort_by = config.get('comment', {}).get('sort_by', 'top')
        logger.info(f"Retrieving comments (sorted by: {sort_by})...")
        comments = reddit.get_comments(submission, sort=sort_by)
        
        if not comments or len(comments) == 0:
            logger.error("No comments found. Cannot proceed without comments.")
            sys.exit(1)
        logger.info(f"Retrieved {len(comments)} comments successfully")

        # ===== STEP 2: Select Best Comment =====
        logger.info("Selecting best comment based on criteria...")
        selected_comment = text.select_best_comment(comments, config)
        
        if not selected_comment:
            logger.error("Could not find any suitable comments matching criteria.")
            sys.exit(1)
            
        comment_text = selected_comment['text']
        comment_id = selected_comment['id']
        
        # Truncate displayed text if too long
        display_text = comment_text
        if len(comment_text) > 300:
            display_text = comment_text[:297] + "..."
            
        logger.info(f"Selected comment (ID: {comment_id}):")
        logger.info(f"\t{display_text}")
        
        # ===== STEP 3: Generate Audio =====
        logger.info("Generating audio for the selected comment...")
        # Initialize the specified TTS engine
        tts_engine_name = config.get('tts_engine', 'gTTS')
        tts_engine_instance = tts.get_tts_engine(tts_engine_name, config)
        # Synthesize speech to cache directory
        audio_path = tts_engine_instance.synthesize(comment_text, comment_id)
        if not audio_path:
            logger.error("Failed to generate audio file.")
            sys.exit(1)
        logger.info(f"Audio generated successfully: {audio_path}")
        # Determine output path for video
        if args.output:
            output_path = args.output
        else:
            output_dir = config.get('video', {}).get('output_directory', 'output')
            Path(output_dir).mkdir(parents=True, exist_ok=True)
            output_filename = f"output_short_{comment_id[:6]}.mp4"
            output_path = os.path.join(output_dir, output_filename)
        logger.info(f"Final output path: {output_path}")

        # ===== STEP 4: Create Video =====
        logger.info("Creating video with background, audio, and text overlay...")
        # Create the video using the video module
        final_video_path = video.create_video(
            background_path=args.background_video,
            audio_path=audio_path,
            comment_text=comment_text,
            output_path=output_path,
            config=config
        )

        if not final_video_path:
            logger.error("Failed to create video")
            sys.exit(1)

        logger.info(f"Video created successfully: {final_video_path}")

        # ===== Cleanup (Optional) =====
        if config.get('cache', {}).get('clean_on_exit', False):
            # TODO: Implement cache cleaning logic if needed
            pass
            
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        print(f"\nERROR: An unexpected error occurred. See logs for details.")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 