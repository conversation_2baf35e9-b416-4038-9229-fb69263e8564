#!/usr/bin/env python3
"""
Fully automated Reddit shorts creator
This script automatically selects a good subreddit and creates a video
"""

import sys
import os
import random
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# List of subreddits that typically have good content for shorts
GOOD_SUBREDDITS = [
    "AmItheAsshole",
    "relationship_advice", 
    "tifu",
    "confession",
    "unpopularopinion",
    "LifeProTips",
    "explainlikeimfive",
    "nostupidquestions",
    "TrueOffMyChest",
    "pettyrevenge",
    "maliciouscompliance",
    "entitledparents",
    "choosingbeggars",
    "ProRevenge"
]

def run_reddit_to_shorts(subreddit, background_video=None, output_path=None):
    """Run the reddit_to_shorts script with the given parameters"""
    cmd = [sys.executable, "reddit_to_shorts.py", subreddit]
    
    if background_video:
        cmd.append(background_video)
    
    if output_path:
        cmd.extend(["-o", output_path])
    
    logger.info(f"Running command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            logger.info("Video created successfully!")
            return True
        else:
            logger.error(f"Script failed with return code {result.returncode}")
            logger.error(f"Error output: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Failed to run script: {e}")
        return False

def create_automated_short(max_attempts=5, background_video=None):
    """
    Automatically create a short video by trying different subreddits
    """
    logger.info("Starting automated short video creation...")
    
    # Shuffle subreddits to get variety
    subreddits = GOOD_SUBREDDITS.copy()
    random.shuffle(subreddits)
    
    attempts = 0
    for subreddit in subreddits:
        if attempts >= max_attempts:
            logger.warning(f"Reached maximum attempts ({max_attempts})")
            break
            
        attempts += 1
        logger.info(f"Attempt {attempts}: Trying subreddit r/{subreddit}")
        
        # Generate output filename with subreddit name
        timestamp = int(time.time()) if 'time' in globals() else random.randint(1000, 9999)
        output_filename = f"automated_short_{subreddit}_{timestamp}.mp4"
        output_path = os.path.join("output", output_filename)
        
        success = run_reddit_to_shorts(subreddit, background_video, output_path)
        
        if success:
            logger.info(f"Successfully created video: {output_path}")
            return output_path
        else:
            logger.warning(f"Failed to create video from r/{subreddit}, trying next...")
    
    logger.error("Failed to create video from any subreddit")
    return None

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Automatically create Reddit shorts videos")
    parser.add_argument("--background", help="Path to background video file")
    parser.add_argument("--max-attempts", type=int, default=5, help="Maximum subreddits to try")
    parser.add_argument("--subreddit", help="Specific subreddit to use (overrides automatic selection)")
    
    args = parser.parse_args()
    
    # Ensure output directory exists
    Path("output").mkdir(exist_ok=True)
    
    if args.subreddit:
        # Use specific subreddit
        logger.info(f"Using specified subreddit: r/{args.subreddit}")
        success = run_reddit_to_shorts(args.subreddit, args.background)
        if success:
            logger.info("Video created successfully!")
        else:
            logger.error("Failed to create video")
            sys.exit(1)
    else:
        # Automatic mode
        result = create_automated_short(args.max_attempts, args.background)
        if result:
            logger.info(f"Automated video creation successful: {result}")
        else:
            logger.error("Automated video creation failed")
            sys.exit(1)

if __name__ == "__main__":
    import time
    main()
