# Configuration for Reddit-to-Shorts

# --- TTS Settings ---
tts_engine: "gTTS" # Engine options: "gTTS", "pyttsx3", "XTTS", "google_cloud", "elevenlabs"

# Google Cloud TTS Settings (only used if tts_engine is "google_cloud")
# Requires Google Cloud credentials configured on your system
google_cloud_tts:
  language_code: "en-US"  # Language code for TTS
  voice_name: "en-US-Studio-O"  # Professional female voice (Studio series are high quality)
  # Other options: en-US-Studio-M (male), en-US-Neural2-F, en-US-Neural2-J
  speaking_rate: 0.9  # 1.0 is normal, <1.0 is slower (good for shorts)
  pitch: 0.0  # 0.0 is normal pitch

# XTTS Specific Settings (only used if tts_engine is "XTTS")
xtts:
  model_path: "tts_models/multilingual/multi-dataset/xtts_v2" # Default XTTS model
  speaker_wav: "../speaker_reference.wav" # Path to a reference speaker WAV file (REQUIRED for XTTS)
  # device: "cuda" # Uncomment and set to "cpu" or "cuda" if needed, otherwise auto-detects

# ElevenLabs Settings (only used if tts_engine is "elevenlabs")
# Requires an ElevenLabs API key (set in config or as ELEVEN_API_KEY environment variable)
elevenlabs:
  api_key: null # Your ElevenLabs API key or set as environment variable ELEVEN_API_KEY
  voice_id: "EXAVITQu4vr4xnSDxMaL" # Adam (male voice) - default choice 
  # Popular voices:
  # - "21m00Tcm4TlvDq8ikWAM" - Rachel (female)
  # - "AZnzlk1XvdvUeBnXmlld" - Domi (female)
  # - "EXAVITQu4vr4xnSDxMaL" - Adam (male)
  # - "ErXwobaYiN019PkySvjV" - Antoni (male)
  model_id: "eleven_turbo_v2" # Turbo model (best balance of quality and speed)
  # Voice settings
  stability: 0.5 # Voice stability (0.0-1.0) - higher values = more consistent, lower = more variable
  similarity_boost: 0.75 # Voice similarity (0.0-1.0) - higher values = closer to original voice
  style: 0.0 # Style exaggeration (0.0-1.0)
  use_speaker_boost: true # Speaker boost for clearer audio

# --- Comment Selection Settings ---
comment:
  sort_by: "top" # Sort comments by: "top", "best", "new", "controversial", "old", "q&a"
  min_words: 10 # Minimum words for a reasonable video duration
  max_words: 350 # Maximum words (adjust based on desired video length)
  target_duration_seconds: 30 # Aim for 20-40 seconds (typical short length)
  min_duration_seconds: 15 # Minimum acceptable duration to ensure 15+ second videos
  # Add any filtering keywords here if needed
  filter_keywords:
    - "http"
    - "www"
    - "link"
    - "source"

# --- Video Output Settings ---
video:
  output_resolution: [1080, 1920] # Width, Height for vertical shorts
  output_fps: 30
  output_filename_template: "output_short_{submission_id}.mp4"
  background_audio_volume: 0.1 # Lower volume for background music/video audio
  subtitle_mode: true # Use timed subtitles instead of static text overlay
  dynamic_subtitles: true # Use word-by-word dynamic subtitles (modern style)
  max_words_per_subtitle: 4 # Maximum words per subtitle segment (1-5 recommended)
  text_overlay:
    enabled: true
    font: "Arial-Bold" # Font for text overlay (ensure it's available on your system)
    fontsize: 72 # Larger font for better mobile readability
    color: "white"
    stroke_color: "black"
    stroke_width: 3 # Thicker stroke for better contrast

# --- Caching Settings ---
cache:
  enabled: true
  directory: "cache"
  # Clear cache options (e.g., clear_on_startup: false) 
# Default background video
default_background_video: "backgrounds/minecraft_style_background.mp4"
