#!/usr/bin/env python3
"""
Video Analysis Tool - Extract frames and analyze text presentation
"""

import os
import sys
import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_frames(video_path, output_dir, num_frames=10):
    """Extract frames from video at regular intervals"""
    try:
        # Create output directory
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # Open video
        cap = cv2.VideoCapture(str(video_path))
        
        if not cap.isOpened():
            logger.error(f"Could not open video: {video_path}")
            return []
        
        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        duration = total_frames / fps if fps > 0 else 0
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        logger.info(f"Video properties:")
        logger.info(f"  Duration: {duration:.2f}s")
        logger.info(f"  FPS: {fps:.2f}")
        logger.info(f"  Resolution: {width}x{height}")
        logger.info(f"  Total frames: {total_frames}")
        
        # Calculate frame intervals
        frame_interval = max(1, total_frames // num_frames)
        extracted_frames = []
        
        for i in range(0, total_frames, frame_interval):
            cap.set(cv2.CAP_PROP_POS_FRAMES, i)
            ret, frame = cap.read()
            
            if ret:
                # Calculate timestamp
                timestamp = i / fps if fps > 0 else 0
                
                # Save frame
                frame_filename = f"frame_{i:06d}_t{timestamp:.2f}s.png"
                frame_path = output_dir / frame_filename
                
                # Convert BGR to RGB for saving
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                cv2.imwrite(str(frame_path), cv2.cvtColor(frame_rgb, cv2.COLOR_RGB2BGR))
                
                extracted_frames.append({
                    'frame_number': i,
                    'timestamp': timestamp,
                    'path': frame_path,
                    'frame': frame_rgb
                })
                
                logger.info(f"Extracted frame {i} at {timestamp:.2f}s")
        
        cap.release()
        logger.info(f"Extracted {len(extracted_frames)} frames to {output_dir}")
        return extracted_frames
        
    except Exception as e:
        logger.error(f"Error extracting frames: {e}")
        return []

def analyze_text_regions(frame, timestamp):
    """Analyze frame to detect text regions and characteristics"""
    try:
        # Convert to grayscale
        gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
        
        # Apply threshold to find text regions (assuming white text on dark background)
        _, thresh = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)
        
        # Find contours (potential text regions)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        text_regions = []
        for contour in contours:
            # Filter small contours
            area = cv2.contourArea(contour)
            if area > 100:  # Minimum area for text
                x, y, w, h = cv2.boundingRect(contour)
                
                # Calculate relative position (0-1 scale)
                rel_x = x / frame.shape[1]
                rel_y = y / frame.shape[0]
                rel_w = w / frame.shape[1]
                rel_h = h / frame.shape[0]
                
                text_regions.append({
                    'bbox': (x, y, w, h),
                    'relative_bbox': (rel_x, rel_y, rel_w, rel_h),
                    'area': area,
                    'aspect_ratio': w / h if h > 0 else 0
                })
        
        # Sort by area (largest first)
        text_regions.sort(key=lambda x: x['area'], reverse=True)
        
        return {
            'timestamp': timestamp,
            'text_regions': text_regions[:5],  # Top 5 largest regions
            'total_text_area': sum(r['area'] for r in text_regions),
            'num_regions': len(text_regions)
        }
        
    except Exception as e:
        logger.error(f"Error analyzing text regions: {e}")
        return None

def create_analysis_report(video_path, frames_data, text_analysis):
    """Create a visual analysis report"""
    try:
        video_name = Path(video_path).stem
        report_dir = Path(f"analysis_{video_name}")
        report_dir.mkdir(exist_ok=True)
        
        # Create a figure with subplots
        fig, axes = plt.subplots(2, 5, figsize=(20, 8))
        fig.suptitle(f'Video Analysis: {video_name}', fontsize=16)
        
        for i, (frame_data, text_data) in enumerate(zip(frames_data[:10], text_analysis[:10])):
            row = i // 5
            col = i % 5
            
            ax = axes[row, col]
            
            # Display frame
            ax.imshow(frame_data['frame'])
            ax.set_title(f't={frame_data["timestamp"]:.1f}s')
            ax.axis('off')
            
            # Draw text region bounding boxes
            if text_data and text_data['text_regions']:
                for region in text_data['text_regions'][:3]:  # Top 3 regions
                    x, y, w, h = region['bbox']
                    rect = plt.Rectangle((x, y), w, h, linewidth=2, 
                                       edgecolor='red', facecolor='none')
                    ax.add_patch(rect)
        
        # Save the analysis plot
        plot_path = report_dir / f"{video_name}_analysis.png"
        plt.tight_layout()
        plt.savefig(plot_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        # Create text report
        report_path = report_dir / f"{video_name}_report.txt"
        with open(report_path, 'w') as f:
            f.write(f"VIDEO ANALYSIS REPORT\n")
            f.write(f"Video: {video_path}\n")
            f.write(f"=" * 50 + "\n\n")
            
            f.write("FRAME-BY-FRAME TEXT ANALYSIS:\n\n")
            
            for frame_data, text_data in zip(frames_data, text_analysis):
                if text_data:
                    f.write(f"Time: {text_data['timestamp']:.2f}s\n")
                    f.write(f"  Text regions found: {text_data['num_regions']}\n")
                    f.write(f"  Total text area: {text_data['total_text_area']}\n")
                    
                    if text_data['text_regions']:
                        f.write(f"  Largest text region:\n")
                        largest = text_data['text_regions'][0]
                        rel_x, rel_y, rel_w, rel_h = largest['relative_bbox']
                        f.write(f"    Position: {rel_x:.2f}, {rel_y:.2f} (relative)\n")
                        f.write(f"    Size: {rel_w:.2f} x {rel_h:.2f} (relative)\n")
                        f.write(f"    Aspect ratio: {largest['aspect_ratio']:.2f}\n")
                    f.write("\n")
            
            # Summary
            f.write("SUMMARY:\n")
            total_regions = sum(t['num_regions'] for t in text_analysis if t)
            avg_regions = total_regions / len(text_analysis) if text_analysis else 0
            f.write(f"  Average text regions per frame: {avg_regions:.1f}\n")
            
            # Analyze text positioning patterns
            positions = []
            for text_data in text_analysis:
                if text_data and text_data['text_regions']:
                    rel_x, rel_y, _, _ = text_data['text_regions'][0]['relative_bbox']
                    positions.append((rel_x, rel_y))
            
            if positions:
                avg_x = sum(p[0] for p in positions) / len(positions)
                avg_y = sum(p[1] for p in positions) / len(positions)
                f.write(f"  Average text position: ({avg_x:.2f}, {avg_y:.2f})\n")
                
                # Determine text positioning style
                if avg_y > 0.7:
                    position_style = "Bottom-positioned (typical for subtitles)"
                elif avg_y < 0.3:
                    position_style = "Top-positioned"
                else:
                    position_style = "Center-positioned"
                
                f.write(f"  Text positioning style: {position_style}\n")
        
        logger.info(f"Analysis report saved to {report_dir}")
        return report_dir
        
    except Exception as e:
        logger.error(f"Error creating analysis report: {e}")
        return None

def analyze_video(video_path, num_frames=10):
    """Main function to analyze video text presentation"""
    logger.info(f"Analyzing video: {video_path}")
    
    if not os.path.exists(video_path):
        logger.error(f"Video file not found: {video_path}")
        return None
    
    # Extract frames
    video_name = Path(video_path).stem
    frames_dir = f"frames_{video_name}"
    frames_data = extract_frames(video_path, frames_dir, num_frames)
    
    if not frames_data:
        logger.error("Failed to extract frames")
        return None
    
    # Analyze text in each frame
    logger.info("Analyzing text regions in frames...")
    text_analysis = []
    
    for frame_data in frames_data:
        analysis = analyze_text_regions(frame_data['frame'], frame_data['timestamp'])
        text_analysis.append(analysis)
    
    # Create report
    report_dir = create_analysis_report(video_path, frames_data, text_analysis)
    
    # Print summary to console
    print("\n" + "="*60)
    print(f"VIDEO ANALYSIS SUMMARY: {video_name}")
    print("="*60)
    
    for i, (frame_data, text_data) in enumerate(zip(frames_data, text_analysis)):
        if text_data:
            print(f"Frame {i+1} (t={text_data['timestamp']:.1f}s): "
                  f"{text_data['num_regions']} text regions, "
                  f"total area: {text_data['total_text_area']}")
    
    return report_dir

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python analyze_video.py <video_path> [num_frames]")
        print("\nAnalyze videos in output folder:")
        
        output_dir = Path("output")
        if output_dir.exists():
            videos = list(output_dir.glob("*.mp4"))
            if videos:
                print("Available videos:")
                for i, video in enumerate(videos, 1):
                    print(f"  {i}. {video.name}")
                
                try:
                    choice = input("\nEnter video number to analyze (or 'all' for all): ").strip()
                    
                    if choice.lower() == 'all':
                        for video in videos:
                            analyze_video(str(video))
                    elif choice.isdigit() and 1 <= int(choice) <= len(videos):
                        selected_video = videos[int(choice) - 1]
                        analyze_video(str(selected_video))
                    else:
                        print("Invalid choice")
                        
                except KeyboardInterrupt:
                    print("\nAnalysis cancelled")
            else:
                print("No videos found in output folder")
        else:
            print("Output folder not found")
        return
    
    video_path = sys.argv[1]
    num_frames = int(sys.argv[2]) if len(sys.argv) > 2 else 10
    
    analyze_video(video_path, num_frames)

if __name__ == "__main__":
    main()
