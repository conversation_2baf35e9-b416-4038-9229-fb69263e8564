# utils/video.py
import os
import logging
import sys
import math
import numpy as np
from pathlib import Path
import tempfile
from moviepy.video.tools.subtitles import SubtitlesClip
from moviepy.editor import TextClip

# Configure logging to output to console with clear formatting
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

print("\n==== Video Module Loaded ====")

# Import moviepy after logging configuration
try:
    # Import moviepy for 1.0.3 version
    import moviepy.editor as mpy
    from moviepy.editor import VideoFileClip, AudioFileClip, CompositeVideoClip
    from moviepy.editor import concatenate_videoclips, ColorClip
except ImportError as e:
    logger.error(f"Failed to import moviepy: {e}")
    logger.error("Please install moviepy with: pip install moviepy==1.0.3")
    sys.exit(1)

# Import PIL for text rendering
try:
    from PIL import Image, ImageDraw, ImageFont
except ImportError as e:
    logger.error(f"Failed to import PIL: {e}")
    logger.error("Please install Pillow with: pip install Pillow")
    sys.exit(1)

# ===== Font Management for Text Overlay =====
def get_available_fonts():
    """Returns a list of fonts available to MoviePy/ImageMagick."""
    try:
        import subprocess
        # This works on most systems with ImageMagick installed
        result = subprocess.run(['magick', 'convert', '-list', 'font'], 
                               capture_output=True, text=True, check=False)
        if result.returncode != 0:
            logger.warning("Could not get list of fonts from ImageMagick")
            return []
        
        # Parse font names from output
        fonts = []
        for line in result.stdout.splitlines():
            if line.strip() and "Font:" in line:
                fonts.append(line.split("Font:")[1].strip())
        return fonts
    except Exception as e:
        logger.warning(f"Error getting available fonts: {e}")
        return []

def get_fallback_font():
    """Returns a font that should be available on most systems."""
    system_fallbacks = [
        'Arial', 'Helvetica', 'DejaVuSans', 'Verdana', 'Tahoma', 
        'Times-New-Roman', 'Courier', 'Consolas'
    ]
    available = get_available_fonts()
    
    # Try system fallbacks first
    for font in system_fallbacks:
        if font in available:
            logger.info(f"Using system font: {font}")
            return font
            
    # If none of our preferred fonts are available, use the first available font
    if available:
        logger.info(f"Using first available font: {available[0]}")
        return available[0]
        
    # Last resort - return Arial and hope for the best
    logger.warning("No fonts found, defaulting to Arial")
    return 'Arial'

# ===== Video Creation Components =====
def prepare_background(video_path, target_duration, target_size=(1080, 1920), fps=30):
    """Creates a background video clip of the target duration and size."""
    logger.info("Preparing video background...")
    
    # Generate black background if no video provided
    if not video_path or not os.path.exists(video_path):
        logger.info("Creating black background")
        return ColorClip(size=target_size, color=(0, 0, 0), duration=target_duration)
    
    # Process video file
    try:
        logger.info(f"Loading background video: {video_path}")
        clip = VideoFileClip(video_path)
        
        # Handle video shorter than target duration
        if clip.duration < target_duration:
            loops = math.ceil(target_duration / clip.duration)
            logger.info(f"Looping video {loops} times to reach target duration")
            clip = concatenate_videoclips([clip] * loops)
            
        # Trim to exact duration
        clip = clip.subclip(0, target_duration)
        
        # Handle aspect ratio
        target_width, target_height = target_size
        target_aspect = target_width / target_height
        current_aspect = clip.w / clip.h
        
        # Resize and crop to target aspect ratio
        if current_aspect > target_aspect:  # Video is wider than target
            clip_resized = clip.resize(height=target_height)
        else:  # Video is taller than target
            clip_resized = clip.resize(width=target_width)
            
        # Center crop
        clip_final = clip_resized.crop(
            x_center=clip_resized.w/2,
            y_center=clip_resized.h/2,
            width=target_width,
            height=target_height
        )
        
        logger.info(f"Background prepared: {clip_final.size} @ {fps}fps")
        return clip_final.set_fps(fps)
        
    except Exception as e:
        logger.error(f"Error preparing background: {e}")
        logger.info("Falling back to black background")
        return ColorClip(size=target_size, color=(0, 0, 0), duration=target_duration)

def create_text_image(text, video_size, config):
    """Creates a PIL Image with text rendered on it for use as a text overlay."""
    print(f"\n==== Creating Text Image ====")
    print(f"Text length: {len(text)} characters")
    
    logger.info("Creating text overlay using PIL...")
    
    # Extract text configuration
    font_size = config.get('fontsize', 70)
    text_color = config.get('color', 'white')
    stroke_color = config.get('stroke_color', 'black')
    stroke_width = config.get('stroke_width', 2)
    
    # Set up dimensions
    width, height = video_size
    img = Image.new('RGBA', (width, height), (0, 0, 0, 0))  # Transparent background
    draw = ImageDraw.Draw(img)
    
    # Try to load a system font
    try:
        # Try different system fonts in order of preference
        system_fonts = ["arial.ttf", "Arial.ttf", "arialbd.ttf", "Verdana.ttf", "times.ttf", "Tahoma.ttf"]
        font = None
        
        # Windows system font path
        font_dir = "C:\\Windows\\Fonts"
        
        # Try each font
        for font_name in system_fonts:
            font_path = os.path.join(font_dir, font_name)
            if os.path.exists(font_path):
                try:
                    font = ImageFont.truetype(font_path, font_size)
                    print(f"Using font: {font_path}")
                    logger.info(f"Using font: {font_path}")
                    break
                except Exception as e:
                    print(f"Could not load font {font_path}: {e}")
                    continue
        
        # Fall back to default if no font loaded
        if font is None:
            print("Falling back to default font")
            font = ImageFont.load_default()
    except Exception as e:
        print(f"Error loading fonts: {e}")
        logger.error(f"Error loading fonts: {e}")
        font = ImageFont.load_default()
    
    # Wrap text to fit screen width
    words = text.split()
    lines = []
    current_line = []
    
    for word in words:
        # Try adding this word to the current line
        test_line = ' '.join(current_line + [word])
        bbox = draw.textbbox((0, 0), test_line, font=font)
        text_width = bbox[2] - bbox[0]
        
        # If it's too wide, start a new line (but only if we have at least one word in the current line)
        if text_width > width * 0.9 and current_line:
            lines.append(' '.join(current_line))
            current_line = [word]
        else:
            current_line.append(word)
    
    # Add the last line if anything is left
    if current_line:
        lines.append(' '.join(current_line))
    
    # Calculate text block height and position vertically centered
    line_height = font_size * 1.2
    text_block_height = len(lines) * line_height
    y_start = (height - text_block_height) // 2
    
    # Draw each line of text
    for i, line in enumerate(lines):
        y = y_start + i * line_height
        
        # Draw text with stroke/outline if specified
        if stroke_width > 0:
            # Draw text outline (basic method - draw the text multiple times with slight offsets)
            for dx, dy in [(0, 1), (1, 0), (0, -1), (-1, 0), (1, 1), (-1, 1), (1, -1), (-1, -1)]:
                draw.text((width//2 + dx, y + dy), line, font=font, fill=stroke_color, anchor="mt")
        
        # Draw the main text
        draw.text((width//2, y), line, font=font, fill=text_color, anchor="mt")
    
    # Save image to a temporary file
    temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
    temp_path = temp_file.name
    temp_file.close()
    
    img.save(temp_path)
    print(f"Text image created: {temp_path}")
    logger.info(f"Text image created and saved to {temp_path}")
    
    return temp_path

def create_text_overlay(text, duration, video_size, config):
    """Creates a text overlay as a video clip using PIL for rendering."""
    try:
        # Create text image
        text_image_path = create_text_image(text, video_size, config)
        
        # Create a video clip from the text image
        text_clip = mpy.ImageClip(text_image_path)
        text_clip = text_clip.set_duration(duration)
        
        # Clean up temporary file (on clip close)
        # Note: We need to keep the file while the clip is in use
        text_clip.on_closed = lambda: os.unlink(text_image_path) if os.path.exists(text_image_path) else None
        
        print(f"Text overlay clip created with duration {duration:.2f}s")
        logger.info(f"Text overlay created successfully")
        return text_clip
        
    except Exception as e:
        print(f"ERROR creating text overlay: {e}")
        logger.error(f"Failed to create text overlay: {e}")
        return None

def create_video_with_subtitles(background_path, audio_path, comment_text, output_path, config):
    """Create a video with timed subtitles instead of static text overlay."""
    print(f"\n==== Creating Video with Subtitles ====")
    print(f"Comment text length: {len(comment_text)} chars")
    print(f"Output path: {output_path}")

    logger.info(f"Creating video with subtitles for comment: {comment_text[:50]}...")

    # Import text module for SRT generation
    from . import text

    # Extract video configuration
    video_config = config.get('video', {})
    target_size = tuple(video_config.get('output_resolution', [1080, 1920]))
    fps = video_config.get('output_fps', 30)
    bg_audio_volume = video_config.get('background_audio_volume', 0.1)

    # Validate inputs
    if not os.path.exists(audio_path):
        print(f"ERROR: Audio file not found: {audio_path}")
        logger.error(f"Audio file not found: {audio_path}")
        return None

    if not comment_text or len(comment_text.strip()) == 0:
        print(f"ERROR: No comment text provided")
        logger.error("No comment text provided")
        return None

    try:
        # 1. Load audio to get duration
        print(f"Loading audio: {audio_path}")
        logger.info(f"Loading audio: {audio_path}")
        audio_clip = AudioFileClip(audio_path)
        duration = audio_clip.duration

        if duration <= 0:
            print(f"ERROR: Audio has no duration")
            logger.error("Audio has no duration")
            return None

        print(f"Audio duration: {duration:.2f}s")
        logger.info(f"Audio duration: {duration:.2f}s")

        # 2. Create background clip
        print(f"Creating background...")
        background = prepare_background(background_path, duration, target_size, fps)

        # 3. Add audio to background
        if isinstance(background, VideoFileClip) and background.audio:
            print(f"Adjusting background audio volume")
            logger.info(f"Adjusting background audio volume to {bg_audio_volume}")
            background = background.volumex(bg_audio_volume)

        background = background.set_audio(audio_clip)

        # 4. Generate SRT file for subtitles
        print(f"Generating subtitles...")
        srt_path = text.generate_srt(comment_text, audio_path, config)

        if srt_path and os.path.exists(srt_path):
            # 5. Create subtitles clip
            print(f"Creating subtitles from: {srt_path}")

            # Configure subtitle style
            font_config = config.get('video', {}).get('text_overlay', {})
            font_size = font_config.get('fontsize', 60)
            font_color = font_config.get('color', 'white')
            stroke_color = font_config.get('stroke_color', 'black')
            stroke_width = font_config.get('stroke_width', 2)

            # Create modern subtitle generator function
            def subtitle_generator(txt):
                try:
                    # Modern styling for dynamic subtitles
                    return TextClip(
                        txt,
                        fontsize=font_size,
                        color=font_color,
                        stroke_color=stroke_color,
                        stroke_width=stroke_width,
                        font='Arial-Bold',  # Bold font for better readability
                        method='caption',   # Better text rendering
                        size=(target_size[0] * 0.9, None)  # 90% of video width
                    ).set_position(('center', 0.75)).set_margin(20)  # Position at 75% height with margin
                except Exception as e:
                    logger.error(f"Error creating modern TextClip: {e}")
                    # Fallback to basic TextClip
                    try:
                        return TextClip(
                            txt,
                            fontsize=font_size,
                            color=font_color,
                            stroke_color=stroke_color,
                            stroke_width=stroke_width,
                            font='Arial'
                        ).set_position(('center', 0.75))
                    except Exception as e2:
                        logger.error(f"Error creating fallback TextClip: {e2}")
                        # Final fallback
                        return TextClip(
                            txt,
                            fontsize=font_size,
                            color=font_color
                        ).set_position(('center', 0.75))

            # Create subtitles clip
            try:
                subtitles = SubtitlesClip(srt_path, subtitle_generator)
                subtitles = subtitles.set_duration(duration)
            except Exception as e:
                logger.error(f"Error creating SubtitlesClip: {e}")
                print(f"WARNING: Could not create subtitles, using static text overlay")
                # Fallback to static text overlay
                subtitles = create_text_overlay(comment_text, duration, target_size, font_config)

            # 6. Composite final video
            print(f"Creating final composite video...")
            final_video = CompositeVideoClip([background, subtitles], size=target_size)

        else:
            print(f"WARNING: Could not generate subtitles, using video without text")
            logger.warning("Could not generate subtitles, proceeding without text overlay")
            final_video = background

        # 7. Write video file
        print(f"Writing video to: {output_path}")
        logger.info(f"Writing video to: {output_path}")
        final_video.write_videofile(
            output_path,
            fps=fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='medium',
            logger=None  # Disable moviepy's internal logger which can be verbose
        )

        # 8. Verify output file
        if not os.path.exists(output_path):
            print(f"ERROR: Output file was not created")
            logger.error("Output file was not created")
            return None

        file_size = os.path.getsize(output_path) / 1024  # KB
        print(f"Video created successfully: {file_size:.1f}KB")
        logger.info(f"Video created successfully: {output_path} ({file_size:.1f}KB)")

        # 9. Clean up resources
        audio_clip.close()
        background.close()
        if 'subtitles' in locals():
            subtitles.close()
        final_video.close()

        # Clean up SRT file if desired
        if srt_path and os.path.exists(srt_path):
            try:
                os.remove(srt_path)
                logger.info("Cleaned up temporary SRT file")
            except:
                pass

        return output_path

    except Exception as e:
        print(f"ERROR creating video: {e}")
        logger.error(f"Error creating video: {e}")
        # Clean up any temp files
        if os.path.exists('temp-audio.m4a'):
            try:
                os.remove('temp-audio.m4a')
            except:
                pass
        return None

def create_video(background_path, audio_path, comment_text, output_path, config):
    """Main function to create a video with text overlay and audio."""
    print(f"\n==== Creating Video ====")
    print(f"Comment text length: {len(comment_text)} chars")
    print(f"Output path: {output_path}")

    logger.info(f"Creating video for comment: {comment_text[:50]}...")

    # Check if subtitles are enabled
    subtitle_mode = config.get('video', {}).get('subtitle_mode', False)

    if subtitle_mode:
        return create_video_with_subtitles(background_path, audio_path, comment_text, output_path, config)

    # Extract video configuration
    video_config = config.get('video', {})
    target_size = tuple(video_config.get('output_resolution', [1080, 1920]))
    fps = video_config.get('output_fps', 30)
    bg_audio_volume = video_config.get('background_audio_volume', 0.1)
    text_config = video_config.get('text_overlay', {})

    print(f"Video config: {target_size}, {fps}fps")
    print(f"Text overlay enabled: {text_config.get('enabled', True)}")

    # Validate inputs
    if not os.path.exists(audio_path):
        print(f"ERROR: Audio file not found: {audio_path}")
        logger.error(f"Audio file not found: {audio_path}")
        return None

    if not comment_text or len(comment_text.strip()) == 0:
        print(f"ERROR: No comment text provided")
        logger.error("No comment text provided")
        return None
    
    try:
        # 1. Load audio to get duration
        print(f"Loading audio: {audio_path}")
        logger.info(f"Loading audio: {audio_path}")
        audio_clip = AudioFileClip(audio_path)
        duration = audio_clip.duration
        
        if duration <= 0:
            print(f"ERROR: Audio has no duration")
            logger.error("Audio has no duration")
            return None
            
        print(f"Audio duration: {duration:.2f}s")
        logger.info(f"Audio duration: {duration:.2f}s")
        
        # 2. Create background clip
        print(f"Creating background...")
        background = prepare_background(background_path, duration, target_size, fps)
        
        # 3. Add audio to background
        if isinstance(background, VideoFileClip) and background.audio:
            print(f"Adjusting background audio volume")
            logger.info(f"Adjusting background audio volume to {bg_audio_volume}")
            background = background.volumex(bg_audio_volume)
            
        background = background.set_audio(audio_clip)
        
        # 4. Create text overlay
        text_enabled = text_config.get('enabled', True)
        clips = [background]
        
        if text_enabled and comment_text:
            print(f"Creating text overlay...")
            text_clip = create_text_overlay(comment_text, duration, target_size, text_config)
            if text_clip:
                print(f"Text overlay created successfully")
                clips.append(text_clip)
                logger.info("Text overlay added to video")
            else:
                print(f"WARNING: Continuing without text overlay")
                logger.warning("Continuing without text overlay")
        
        # 5. Composite and write final video
        print(f"Creating final composite video...")
        final_video = CompositeVideoClip(clips, size=target_size)
        
        print(f"Writing video to: {output_path}")
        logger.info(f"Writing video to: {output_path}")
        final_video.write_videofile(
            output_path,
            fps=fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            preset='medium',
            logger=None  # Disable moviepy's internal logger which can be verbose
        )
        
        # 6. Verify output file
        if not os.path.exists(output_path):
            print(f"ERROR: Output file was not created")
            logger.error("Output file was not created")
            return None
            
        file_size = os.path.getsize(output_path) / 1024  # KB
        print(f"Video created successfully: {file_size:.1f}KB")
        logger.info(f"Video created successfully: {output_path} ({file_size:.1f}KB)")
        
        # 7. Clean up resources
        audio_clip.close()
        background.close()
        if 'text_clip' in locals() and text_clip:
            text_clip.close()
        final_video.close()
        
        return output_path
        
    except Exception as e:
        print(f"ERROR creating video: {e}")
        logger.error(f"Error creating video: {e}")
        # Clean up any temp files
        if os.path.exists('temp-audio.m4a'):
            try:
                os.remove('temp-audio.m4a')
            except:
                pass
        return None

def burn_subtitles(background_clip, audio_clip, srt_file, config):
    """Overlay timed subtitles onto a background video+audio clip."""
    # font specs from your config
    font     = config['FONT']
    fontsize = config['FONTSIZE']
    color    = config['TEXT_COLOR']
    stroke   = config['STROKE_COLOR']
    # generator for each subtitle line
    generator = lambda txt: TextClip(
        txt, font=font, fontsize=fontsize, color=color,
        stroke_color=stroke, stroke_width=config.get('STROKE_WIDTH',2)
    )
    subs = SubtitlesClip(srt_file, generator)
    subs = subs.set_position(('center','bottom')).set_duration(audio_clip.duration)
    # composite them
    final = CompositeVideoClip([background_clip.set_audio(audio_clip), subs])
    return final