#!/usr/bin/env python3
"""
Download background videos for Reddit shorts
"""

import os
import sys
import requests
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_file(url, filename):
    """Download a file from URL"""
    try:
        logger.info(f"Downloading {filename}...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\rProgress: {percent:.1f}%", end='', flush=True)
        
        print()  # New line after progress
        logger.info(f"Downloaded {filename} successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to download {filename}: {e}")
        return False

def download_minecraft_background():
    """Download a Minecraft parkour background video"""
    backgrounds_dir = Path("backgrounds")
    backgrounds_dir.mkdir(exist_ok=True)
    
    # List of free Minecraft background videos (these are example URLs - you'd need real ones)
    minecraft_videos = [
        {
            "name": "minecraft_parkour_1.mp4",
            "url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",  # Placeholder
            "description": "Minecraft Parkour Background 1"
        }
    ]
    
    # For now, let's create a simple colored background as a fallback
    logger.info("Creating a simple background video...")
    
    try:
        import moviepy.editor as mpy
        
        # Create a simple animated background
        def make_frame(t):
            # Create a gradient background that changes over time
            import numpy as np
            
            # Video dimensions for vertical format
            width, height = 1080, 1920
            
            # Create a gradient that shifts over time
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # Create a moving gradient
            for y in range(height):
                for x in range(width):
                    # Create a moving pattern
                    r = int(128 + 127 * np.sin(0.01 * x + 0.1 * t))
                    g = int(128 + 127 * np.sin(0.01 * y + 0.1 * t + 2))
                    b = int(128 + 127 * np.sin(0.01 * (x + y) + 0.1 * t + 4))
                    
                    frame[y, x] = [r, g, b]
            
            return frame
        
        # Create a 60-second background video
        duration = 60
        background_clip = mpy.VideoClip(make_frame, duration=duration)
        background_clip = background_clip.set_fps(30)
        
        output_path = backgrounds_dir / "animated_background.mp4"
        logger.info(f"Creating animated background: {output_path}")
        
        background_clip.write_videofile(
            str(output_path),
            codec='libx264',
            preset='medium',
            logger=None
        )
        
        background_clip.close()
        logger.info("Animated background created successfully!")
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Failed to create background: {e}")
        return None

def download_simple_minecraft_style():
    """Create a simple Minecraft-style background"""
    backgrounds_dir = Path("backgrounds")
    backgrounds_dir.mkdir(exist_ok=True)
    
    try:
        import moviepy.editor as mpy
        import numpy as np
        
        def make_minecraft_frame(t):
            # Create a simple block-style pattern
            width, height = 1080, 1920
            block_size = 60
            
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # Minecraft-like colors
            colors = [
                [34, 139, 34],   # Forest Green
                [139, 69, 19],   # Saddle Brown  
                [70, 130, 180],  # Steel Blue
                [160, 82, 45],   # Saddle Brown
                [50, 205, 50],   # Lime Green
                [30, 144, 255],  # Dodger Blue
            ]
            
            # Create moving blocks
            offset_x = int(t * 20) % block_size
            offset_y = int(t * 15) % block_size
            
            for y in range(0, height, block_size):
                for x in range(0, width, block_size):
                    # Choose color based on position and time
                    color_idx = ((x // block_size) + (y // block_size) + int(t * 0.5)) % len(colors)
                    color = colors[color_idx]
                    
                    # Apply offset for movement
                    start_x = max(0, x - offset_x)
                    end_x = min(width, x + block_size - offset_x)
                    start_y = max(0, y - offset_y)
                    end_y = min(height, y + block_size - offset_y)
                    
                    if start_x < end_x and start_y < end_y:
                        frame[start_y:end_y, start_x:end_x] = color
            
            return frame
        
        # Create a 60-second Minecraft-style background
        duration = 60
        background_clip = mpy.VideoClip(make_minecraft_frame, duration=duration)
        background_clip = background_clip.set_fps(30)
        
        output_path = backgrounds_dir / "minecraft_style_background.mp4"
        logger.info(f"Creating Minecraft-style background: {output_path}")
        
        background_clip.write_videofile(
            str(output_path),
            codec='libx264',
            preset='medium',
            logger=None
        )
        
        background_clip.close()
        logger.info("Minecraft-style background created successfully!")
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Failed to create Minecraft-style background: {e}")
        return None

def main():
    """Main function"""
    logger.info("Setting up background videos...")
    
    # Try to create a Minecraft-style background
    minecraft_bg = download_simple_minecraft_style()
    
    if minecraft_bg:
        logger.info(f"Background video ready: {minecraft_bg}")
        
        # Update config to use this background by default
        config_path = Path("config.yaml")
        if config_path.exists():
            with open(config_path, 'r') as f:
                content = f.read()
            
            # Add background video setting
            if 'default_background_video:' not in content:
                content += f"\n# Default background video\ndefault_background_video: \"{minecraft_bg}\"\n"
                
                with open(config_path, 'w') as f:
                    f.write(content)
                
                logger.info("Updated config.yaml with default background video")
    
    else:
        logger.error("Failed to create background video")

if __name__ == "__main__":
    main()
