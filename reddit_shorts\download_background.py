#!/usr/bin/env python3
"""
Download background videos for Reddit shorts
"""

import os
import sys
import requests
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_file(url, filename):
    """Download a file from URL"""
    try:
        logger.info(f"Downloading {filename}...")
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        percent = (downloaded / total_size) * 100
                        print(f"\rProgress: {percent:.1f}%", end='', flush=True)
        
        print()  # New line after progress
        logger.info(f"Downloaded {filename} successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to download {filename}: {e}")
        return False

def download_minecraft_background():
    """Download a Minecraft parkour background video"""
    backgrounds_dir = Path("backgrounds")
    backgrounds_dir.mkdir(exist_ok=True)
    
    # List of free Minecraft background videos (these are example URLs - you'd need real ones)
    minecraft_videos = [
        {
            "name": "minecraft_parkour_1.mp4",
            "url": "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",  # Placeholder
            "description": "Minecraft Parkour Background 1"
        }
    ]
    
    # For now, let's create a simple colored background as a fallback
    logger.info("Creating a simple background video...")
    
    try:
        import moviepy.editor as mpy
        
        # Create a simple animated background
        def make_frame(t):
            # Create a gradient background that changes over time
            import numpy as np
            
            # Video dimensions for vertical format
            width, height = 1080, 1920
            
            # Create a gradient that shifts over time
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # Create a moving gradient
            for y in range(height):
                for x in range(width):
                    # Create a moving pattern
                    r = int(128 + 127 * np.sin(0.01 * x + 0.1 * t))
                    g = int(128 + 127 * np.sin(0.01 * y + 0.1 * t + 2))
                    b = int(128 + 127 * np.sin(0.01 * (x + y) + 0.1 * t + 4))
                    
                    frame[y, x] = [r, g, b]
            
            return frame
        
        # Create a 60-second background video
        duration = 60
        background_clip = mpy.VideoClip(make_frame, duration=duration)
        background_clip = background_clip.set_fps(30)
        
        output_path = backgrounds_dir / "animated_background.mp4"
        logger.info(f"Creating animated background: {output_path}")
        
        background_clip.write_videofile(
            str(output_path),
            codec='libx264',
            preset='medium',
            logger=None
        )
        
        background_clip.close()
        logger.info("Animated background created successfully!")
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Failed to create background: {e}")
        return None

def download_simple_minecraft_style():
    """Create a proper Minecraft-style background with textures and depth"""
    backgrounds_dir = Path("backgrounds")
    backgrounds_dir.mkdir(exist_ok=True)

    try:
        import moviepy.editor as mpy
        import numpy as np

        def make_minecraft_frame(t):
            # Create a proper Minecraft-style pattern
            width, height = 1080, 1920
            block_size = 80  # Larger blocks for better Minecraft feel

            frame = np.zeros((height, width, 3), dtype=np.uint8)

            # Proper Minecraft block colors (more realistic)
            minecraft_blocks = [
                [34, 139, 34],    # Grass Green
                [139, 69, 19],    # Oak Wood Brown
                [128, 128, 128],  # Stone Gray
                [160, 82, 45],    # Dirt Brown
                [50, 205, 50],    # Lime Green (Slime)
                [70, 70, 70],     # Cobblestone
                [101, 67, 33],    # Dark Oak
                [85, 107, 47],    # Dark Olive Green
            ]

            # Create slow parallax movement for depth
            slow_offset_x = int(t * 10) % block_size
            slow_offset_y = int(t * 8) % block_size

            for y in range(-block_size, height + block_size, block_size):
                for x in range(-block_size, width + block_size, block_size):
                    # Create more natural block distribution
                    block_x = x + slow_offset_x
                    block_y = y + slow_offset_y

                    # Use position-based seeding for consistent pattern
                    seed = (block_x // block_size) * 31 + (block_y // block_size) * 17
                    color_idx = seed % len(minecraft_blocks)
                    base_color = minecraft_blocks[color_idx]

                    # Add texture variation within each block
                    for sub_y in range(max(0, block_y), min(height, block_y + block_size)):
                        for sub_x in range(max(0, block_x), min(width, block_x + block_size)):
                            if 0 <= sub_x < width and 0 <= sub_y < height:
                                # Add subtle texture variation
                                variation = np.sin(sub_x * 0.1) * np.cos(sub_y * 0.1) * 15

                                # Add 3D-like shading
                                edge_factor = 1.0
                                if sub_x - block_x < 5:  # Left edge
                                    edge_factor = 0.8
                                elif sub_x - block_x > block_size - 5:  # Right edge
                                    edge_factor = 0.6
                                if sub_y - block_y < 5:  # Top edge
                                    edge_factor *= 1.2
                                elif sub_y - block_y > block_size - 5:  # Bottom edge
                                    edge_factor *= 0.7

                                # Apply color with variation and shading
                                final_color = [
                                    max(0, min(255, int(base_color[0] * edge_factor + variation))),
                                    max(0, min(255, int(base_color[1] * edge_factor + variation))),
                                    max(0, min(255, int(base_color[2] * edge_factor + variation)))
                                ]

                                frame[sub_y, sub_x] = final_color

            return frame
        
        # Create a shorter, faster Minecraft-style background
        duration = 15  # Shorter for faster generation
        background_clip = mpy.VideoClip(make_minecraft_frame, duration=duration)
        background_clip = background_clip.set_fps(15)  # Lower FPS for speed

        output_path = backgrounds_dir / "minecraft_style_background.mp4"
        logger.info(f"Creating Minecraft-style background: {output_path}")

        background_clip.write_videofile(
            str(output_path),
            codec='libx264',
            preset='ultrafast',  # Fastest encoding
            logger=None,
            verbose=False,
            ffmpeg_params=['-crf', '28']  # Lower quality for speed
        )
        
        background_clip.close()
        logger.info("Minecraft-style background created successfully!")
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Failed to create Minecraft-style background: {e}")
        return None

def main():
    """Main function"""
    logger.info("Setting up background videos...")
    
    # Try to create a Minecraft-style background
    minecraft_bg = download_simple_minecraft_style()
    
    if minecraft_bg:
        logger.info(f"Background video ready: {minecraft_bg}")
        
        # Update config to use this background by default
        config_path = Path("config.yaml")
        if config_path.exists():
            with open(config_path, 'r') as f:
                content = f.read()
            
            # Add background video setting
            if 'default_background_video:' not in content:
                content += f"\n# Default background video\ndefault_background_video: \"{minecraft_bg}\"\n"
                
                with open(config_path, 'w') as f:
                    f.write(content)
                
                logger.info("Updated config.yaml with default background video")
    
    else:
        logger.error("Failed to create background video")

if __name__ == "__main__":
    main()
